package com.gl.service.commercial.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.commercial.service.CommercialService;
import com.gl.service.commercial.vo.CommercialVo;
import com.gl.service.commercial.vo.dto.CommercialDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CommercialController单元测试类
 * 测试商户管理控制器的所有Web接口
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class)
@AutoConfigureMockMvc
@DisplayName("商户管理控制器测试")
class CommercialControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CommercialService commercialService;

    @Autowired
    private ObjectMapper objectMapper;

    private CommercialDto testDto;
    private Result testResult;
    private List<CommercialVo> testCommercialVos;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testDto = new CommercialDto();
        testDto.setPageNumber(0);
        testDto.setPageSize(10);
        testDto.setShopName("测试店铺");
        testDto.setShopId(1L);
        testDto.setSearchCondition("测试用户");
        testDto.setBeginTime("2024-01-01 00:00:00");
        testDto.setEndTime("2024-12-31 23:59:59");

        // 初始化商户数据
        testCommercialVos = new ArrayList<>();
        CommercialVo vo1 = new CommercialVo();
        vo1.setId(1L);
        vo1.setNickname("测试用户1");
        vo1.setPhone("13800138001");
        vo1.setGender(1);
        vo1.setAuthTime(new Date());
        vo1.setShopNames("测试店铺1");
        vo1.setDeviceNames("设备1");
        vo1.setDeviceNum(2);
        testCommercialVos.add(vo1);

        // 初始化返回结果
        testResult = Result.success();
        testResult.addData("total", 1L);
        testResult.addData("result", testCommercialVos);
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 正常查询场景")
    @WithMockUser(authorities = "dub:commercial:list")
    void testList_NormalQuery_ShouldReturnSuccessResponse() throws Exception {
        // Given - 准备测试数据
        when(commercialService.list(any(CommercialDto.class), eq(1))).thenReturn(testResult);

        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .param("pageNumber", "0")
                .param("pageSize", "10")
                .param("shopName", "测试店铺")
                .param("shopId", "1")
                .param("searchCondition", "测试用户")
                .param("beginTime", "2024-01-01 00:00:00")
                .param("endTime", "2024-12-31 23:59:59")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.result").isArray())
                .andExpect(jsonPath("$.data.result[0].id").value(1))
                .andExpect(jsonPath("$.data.result[0].nickname").value("测试用户1"))
                .andExpect(jsonPath("$.data.result[0].phone").value("13800138001"));

        // 验证服务方法调用
        verify(commercialService).list(any(CommercialDto.class), eq(1));
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testList_NoPermission_ShouldReturnForbidden() throws Exception {
        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 验证服务方法未被调用
        verify(commercialService, never()).list(any(CommercialDto.class), anyInt());
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 未认证用户")
    void testList_Unauthenticated_ShouldReturnUnauthorized() throws Exception {
        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        // 验证服务方法未被调用
        verify(commercialService, never()).list(any(CommercialDto.class), anyInt());
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 空参数查询")
    @WithMockUser(authorities = "dub:commercial:list")
    void testList_EmptyParameters_ShouldReturnSuccessResponse() throws Exception {
        // Given - 准备测试数据
        when(commercialService.list(any(CommercialDto.class), eq(1))).thenReturn(testResult);

        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        // 验证服务方法调用
        verify(commercialService).list(any(CommercialDto.class), eq(1));
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 服务层返回空结果")
    @WithMockUser(authorities = "dub:commercial:list")
    void testList_ServiceReturnsEmptyResult_ShouldReturnEmptyResponse() throws Exception {
        // Given - 服务层返回空结果
        Result emptyResult = Result.success();
        emptyResult.addData("total", 0L);
        emptyResult.addData("result", new ArrayList<>());
        when(commercialService.list(any(CommercialDto.class), eq(1))).thenReturn(emptyResult);

        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .param("pageNumber", "0")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.result").isArray())
                .andExpect(jsonPath("$.data.result").isEmpty());

        // 验证服务方法调用
        verify(commercialService).list(any(CommercialDto.class), eq(1));
    }

    @Test
    @DisplayName("测试GET /commercial接口 - 参数验证边界值")
    @WithMockUser(authorities = "dub:commercial:list")
    void testList_BoundaryValues_ShouldHandleCorrectly() throws Exception {
        // Given - 准备测试数据
        when(commercialService.list(any(CommercialDto.class), eq(1))).thenReturn(testResult);

        // When & Then - 执行请求并验证结果
        mockMvc.perform(get("/commercial")
                .param("pageNumber", "0")
                .param("pageSize", "1")
                .param("shopId", "999999")
                .param("shopName", "很长的店铺名称测试边界值处理能力")
                .param("searchCondition", "很长的搜索条件测试边界值处理能力")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        // 验证服务方法调用
        verify(commercialService).list(any(CommercialDto.class), eq(1));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 正常导出场景")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_NormalExport_ShouldReturnExcelFile() throws Exception {
        // Given - 准备测试数据
        doNothing().when(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));

        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isOk());

        // 验证服务方法调用
        verify(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testExportList_NoPermission_ShouldReturnForbidden() throws Exception {
        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 验证服务方法未被调用
        verify(commercialService, never()).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 未认证用户")
    void testExportList_Unauthenticated_ShouldReturnUnauthorized() throws Exception {
        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        // 验证服务方法未被调用
        verify(commercialService, never()).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 空请求体")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_EmptyRequestBody_ShouldHandleGracefully() throws Exception {
        // Given - 准备测试数据
        doNothing().when(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));

        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andDo(print())
                .andExpect(status().isOk());

        // 验证服务方法调用
        verify(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 无效JSON格式")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_InvalidJsonFormat_ShouldReturnBadRequest() throws Exception {
        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        // 验证服务方法未被调用
        verify(commercialService, never()).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 服务层抛出IO异常")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_ServiceThrowsIOException_ShouldHandleException() throws Exception {
        // Given - 服务层抛出IO异常
        doThrow(new RuntimeException("IO异常")).when(commercialService)
                .exportList(any(CommercialDto.class), any(HttpServletResponse.class));

        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        // 验证服务方法调用
        verify(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 缺少CSRF Token")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_MissingCSRFToken_ShouldReturnForbidden() throws Exception {
        // When & Then - 执行请求并验证结果（不添加CSRF token）
        mockMvc.perform(post("/commercial/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 验证服务方法未被调用
        verify(commercialService, never()).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试POST /commercial/export接口 - 完整参数导出")
    @WithMockUser(authorities = "dub:commercial:export")
    void testExportList_FullParametersExport_ShouldProcessCorrectly() throws Exception {
        // Given - 准备完整参数的DTO
        CommercialDto fullDto = new CommercialDto();
        fullDto.setShopName("完整测试店铺");
        fullDto.setShopId(999L);
        fullDto.setSearchCondition("完整搜索条件");
        fullDto.setBeginTime("2024-01-01 00:00:00");
        fullDto.setEndTime("2024-12-31 23:59:59");

        doNothing().when(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));

        // When & Then - 执行请求并验证结果
        mockMvc.perform(post("/commercial/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(fullDto)))
                .andDo(print())
                .andExpect(status().isOk());

        // 验证服务方法调用
        verify(commercialService).exportList(any(CommercialDto.class), any(HttpServletResponse.class));
    }
}
